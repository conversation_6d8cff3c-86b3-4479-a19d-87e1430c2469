// lib/services/logservice.dart

import 'package:erp_logs/models/log.dart';
import 'package:http/http.dart' as http;

class LogService {
  // Helper to format DateTime into 'YYYY-MM-DD'
  String _formatDate(DateTime date) {
    String year = date.year.toString();
    String month = date.month.toString().padLeft(2, '0');
    String day = date.day.toString().padLeft(2, '0');
    return '$year-$month-$day';
  }

  // The method now takes the required parameters
  Future<List<Logs?>> getLogs({
    required String school,
    required DateTime date,
  }) async {
    var client = http.Client();
    final formattedDate = _formatDate(date);

    // The URI is now built dynamically
    var uri = Uri.parse(
      'https://deeplabsystems2-001-site12.atempurl.com/api/users/$school/logs/$formattedDate',
    );

    try {
      var response = await client.get(uri);
      if (response.statusCode == 200) {
        // Handle empty response gracefully
        if (response.body.isEmpty || response.body == "[]") {
          return [];
        }
        return logsFromJson(response.body);
      } else {
        // Provide more context on failure
        throw Exception(
          'Failed to load logs. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Failed to load logs: ${e.toString()}');
    }
  }
}
