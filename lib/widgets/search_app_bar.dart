import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../controllers/log_controller.dart';
import '../controllers/theme_controller.dart';
import 'theme_toggle.dart';

class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final LogController controller;
  final ThemeController themeController;
  final VoidCallback onDateTap;
  final VoidCallback onSortTap;

  const SearchAppBar({
    super.key,
    required this.controller,
    required this.themeController,
    required this.onDateTap,
    required this.onSortTap,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();
}

class _SearchAppBarState extends State<SearchAppBar>
    with SingleTickerProviderStateMixin {
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    widget.controller.updateSearchQuery(_searchController.text);
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (_isSearching) {
        _animationController.forward();
      } else {
        _animationController.reverse();
        _searchController.clear();
      }
    });
  }

  Widget _buildSearchField() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: TextField(
        controller: _searchController,
        autofocus: true,
        decoration: InputDecoration(
          hintText: 'Search logs...',
          border: InputBorder.none,
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildAppBarTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          DateFormat('EEE, MMM d').format(widget.controller.selectedDate),
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        Text(
          widget.controller.selectedSchool.name,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AppBar(
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      scrolledUnderElevation: 1,
      title: _isSearching ? _buildSearchField() : _buildAppBarTitle(),
      actions:
          _isSearching
              ? [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _toggleSearch,
                  tooltip: 'Close search',
                ),
              ]
              : [
                IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: _toggleSearch,
                  tooltip: 'Search logs',
                ),
                IconButton(
                  icon: const Icon(Icons.sort),
                  onPressed: widget.onSortTap,
                  tooltip: 'Sort options',
                ),
                ThemeToggle(themeController: widget.themeController),
                PopupMenuButton<School>(
                  onSelected: (School school) {
                    widget.controller.updateSelectedSchool(school);
                  },
                  icon: const Icon(Icons.school_outlined),
                  tooltip: 'Select school',
                  itemBuilder:
                      (context) =>
                          School.values
                              .map(
                                (school) => PopupMenuItem<School>(
                                  value: school,
                                  child: Row(
                                    children: [
                                      Icon(
                                        school ==
                                                widget.controller.selectedSchool
                                            ? Icons.radio_button_checked
                                            : Icons.radio_button_unchecked,
                                        size: 20,
                                        color:
                                            school ==
                                                    widget
                                                        .controller
                                                        .selectedSchool
                                                ? theme.colorScheme.primary
                                                : theme
                                                    .colorScheme
                                                    .onSurfaceVariant,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(school.name),
                                    ],
                                  ),
                                ),
                              )
                              .toList(),
                ),
                IconButton(
                  icon: const Icon(Icons.calendar_month_outlined),
                  onPressed: widget.onDateTap,
                  tooltip: 'Select date',
                ),
              ],
    );
  }
}
