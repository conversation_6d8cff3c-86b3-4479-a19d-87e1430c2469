import 'package:flutter/material.dart';
import '../controllers/log_controller.dart';
import '../components/log_card.dart';
import 'skeleton_loader.dart';

class LogListView extends StatelessWidget {
  final LogController controller;

  const LogListView({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        if (controller.isLoading) {
          return const SkeletonListView();
        }

        if (!controller.hasLogs) {
          return _buildEmptyState(context);
        }

        return RefreshIndicator(
          onRefresh: controller.refreshLogs,
          child: ListView.builder(
            padding: const EdgeInsets.only(top: 8.0, bottom: 80.0),
            itemCount: controller.filteredLogs.length,
            itemBuilder: (context, index) {
              final log = controller.filteredLogs[index];
              return AnimatedContainer(
                duration: Duration(milliseconds: 200 + (index * 50)),
                curve: Curves.easeOutCubic,
                child: Log<PERSON>ard(log: log),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final hasSearchQuery = controller.searchQuery.isNotEmpty;

    return RefreshIndicator(
      onRefresh: controller.refreshLogs,
      child: CustomScrollView(
        slivers: [
          SliverFillRemaining(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    hasSearchQuery ? Icons.search_off : Icons.event_note,
                    size: 64,
                    color: theme.colorScheme.onSurfaceVariant.withValues(
                      alpha: 0.6,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    hasSearchQuery
                        ? 'No results found'
                        : 'No logs for this day',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    hasSearchQuery
                        ? 'Try adjusting your search terms'
                        : 'Pull down to refresh or try a different date',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant.withValues(
                        alpha: 0.8,
                      ),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (hasSearchQuery) ...[
                    const SizedBox(height: 16),
                    FilledButton.tonal(
                      onPressed: () => controller.updateSearchQuery(''),
                      child: const Text('Clear search'),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
