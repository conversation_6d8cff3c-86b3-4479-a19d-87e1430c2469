import 'package:flutter/material.dart';
import '../controllers/theme_controller.dart';

class ThemeToggle extends StatefulWidget {
  final ThemeController themeController;

  const ThemeToggle({super.key, required this.themeController});

  @override
  State<ThemeToggle> createState() => _ThemeToggleState();
}

class _ThemeToggleState extends State<ThemeToggle>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleTheme() {
    _animationController.forward().then((_) {
      widget.themeController.toggleTheme();
      _animationController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: widget.themeController,
      builder: (context, child) {
        final isDark = widget.themeController.isDarkMode;

        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: IconButton(
                  onPressed: _toggleTheme,
                  icon: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 200),
                    transitionBuilder: (child, animation) {
                      return ScaleTransition(scale: animation, child: child);
                    },
                    child: Icon(
                      isDark ? Icons.light_mode : Icons.dark_mode,
                      key: ValueKey(isDark),
                    ),
                  ),
                  tooltip:
                      isDark ? 'Switch to light mode' : 'Switch to dark mode',
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class ThemeBottomSheet extends StatelessWidget {
  final ThemeController themeController;

  const ThemeBottomSheet({super.key, required this.themeController});

  static void show(BuildContext context, ThemeController themeController) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ThemeBottomSheet(themeController: themeController),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Icon(Icons.palette, color: theme.colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'Theme Settings',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Theme options
          ListenableBuilder(
            listenable: themeController,
            builder: (context, child) {
              return Column(
                children: [
                  _buildThemeOption(
                    context,
                    AppThemeMode.light,
                    Icons.light_mode,
                    'Light',
                    'Always use light theme',
                  ),
                  _buildThemeOption(
                    context,
                    AppThemeMode.dark,
                    Icons.dark_mode,
                    'Dark',
                    'Always use dark theme',
                  ),
                  _buildThemeOption(
                    context,
                    AppThemeMode.system,
                    Icons.brightness_auto,
                    'System',
                    'Follow system setting',
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    AppThemeMode mode,
    IconData icon,
    String title,
    String subtitle,
  ) {
    final theme = Theme.of(context);
    final isSelected = themeController.themeMode == mode;

    return ListTile(
      leading: Icon(
        icon,
        color:
            isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
      ),
      title: Text(
        title,
        style: TextStyle(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: theme.colorScheme.onSurfaceVariant,
          fontSize: 12,
        ),
      ),
      trailing:
          isSelected
              ? Icon(Icons.check, color: theme.colorScheme.primary)
              : null,
      onTap: () {
        themeController.setThemeMode(mode);
        Navigator.of(context).pop();
      },
    );
  }
}
