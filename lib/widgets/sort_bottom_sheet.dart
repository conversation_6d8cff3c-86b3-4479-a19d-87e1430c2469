import 'package:flutter/material.dart';
import '../controllers/log_controller.dart';

class SortBottomSheet extends StatelessWidget {
  final LogController controller;

  const SortBottomSheet({super.key, required this.controller});

  static void show(BuildContext context, LogController controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SortBottomSheet(controller: controller),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Icon(Icons.sort, color: theme.colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'Sort Options',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Sort options
          ...SortOption.values.map(
            (option) => _buildSortOption(
              context,
              option,
              controller.sortOption == option,
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildSortOption(
    BuildContext context,
    SortOption option,
    bool isSelected,
  ) {
    final theme = Theme.of(context);

    return ListTile(
      leading: Icon(
        _getSortIcon(option),
        color:
            isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
      ),
      title: Text(
        controller.getSortOptionName(option),
        style: TextStyle(
          color:
              isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
      trailing:
          isSelected
              ? Icon(Icons.check, color: theme.colorScheme.primary)
              : null,
      onTap: () {
        controller.updateSortOption(option);
        Navigator.of(context).pop();
      },
    );
  }

  IconData _getSortIcon(SortOption option) {
    switch (option) {
      case SortOption.timeDesc:
        return Icons.access_time;
      case SortOption.timeAsc:
        return Icons.history;
      case SortOption.typeAsc:
        return Icons.category;
      case SortOption.operationAsc:
        return Icons.settings;
    }
  }
}
