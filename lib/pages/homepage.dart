import 'package:flutter/material.dart';
import '../controllers/log_controller.dart';
import '../controllers/theme_controller.dart';
import '../widgets/search_app_bar.dart';
import '../widgets/log_list_view.dart';
import '../widgets/sort_bottom_sheet.dart';

class Homepage extends StatefulWidget {
  final ThemeController themeController;

  const Homepage({super.key, required this.themeController});

  @override
  State<Homepage> createState() => _HomepageState();
}

class _HomepageState extends State<Homepage> {
  late final LogController _controller;

  @override
  void initState() {
    super.initState();
    _controller = LogController();
    _controller.initialize();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _pickDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _controller.selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      _controller.updateSelectedDate(picked);
    }
  }

  void _showSortOptions() {
    SortBottomSheet.show(context, _controller);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: SearchAppBar(
        controller: _controller,
        themeController: widget.themeController,
        onDateTap: () => _pickDate(context),
        onSortTap: _showSortOptions,
      ),
      body: ListenableBuilder(
        listenable: _controller,
        builder: (context, child) {
          // Handle errors
          return FutureBuilder(
            future: null, // We handle async operations in the controller
            builder: (context, snapshot) {
              return LogListView(controller: _controller);
            },
          );
        },
      ),
    );
  }
}
